using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StackExchange.Redis;
using System.Threading.Tasks;
using VidCompressor.Services;
using System.IO;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class VideosController : ControllerBase
{
    private readonly GooglePhotosService _googlePhotosService;
    private readonly IConnectionMultiplexer _redis;

    public VideosController(GooglePhotosService googlePhotosService, IConnectionMultiplexer redis)
    {
        _googlePhotosService = googlePhotosService;
        _redis = redis;
    }

    [HttpGet]
    public async Task<IActionResult> GetVideos()
    {
        var accessToken = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
        var videos = await _googlePhotosService.GetVideosAsync(accessToken);
        return Ok(videos);
    }

    [HttpGet("{mediaItemId}/info")]
    public async Task<IActionResult> GetVideoInfo(string mediaItemId)
    {
        var accessToken = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
        var videoInfo = await _googlePhotosService.GetVideoInfoAsync(accessToken, mediaItemId);
        return Ok(videoInfo);
    }

    [HttpGet("{mediaItemId}/download")]
    public async Task<IActionResult> DownloadVideo(string mediaItemId)
    {
        var accessToken = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
        var videoStream = await _googlePhotosService.DownloadVideoAsync(accessToken, mediaItemId);
        return File(videoStream, "video/mp4", "video.mp4");
    }

    [HttpPost("{mediaItemId}/compress")]
    public async Task<IActionResult> CompressVideo(string mediaItemId, [FromBody] CompressVideoRequest request)
    {
        var accessToken = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");
        var quality = request?.Quality ?? "medium";
        var subscriber = _redis.GetSubscriber();
        await subscriber.PublishAsync(RedisChannel.Literal("video-compression-jobs"), $"{accessToken}:{mediaItemId}:{quality}");
        return Ok(new { message = "Video compression started" });
    }

    [HttpPost("upload")]
    public async Task<IActionResult> UploadVideo(IFormFile videoFile)
    {
        if (videoFile == null || videoFile.Length == 0)
        {
            return BadRequest(new { message = "No video file provided" });
        }

        var accessToken = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

        // Save the uploaded file temporarily
        var tempPath = Path.Combine(Path.GetTempPath(), $"upload_{Guid.NewGuid()}.mp4");

        try
        {
            using (var stream = new FileStream(tempPath, FileMode.Create))
            {
                await videoFile.CopyToAsync(stream);
            }

            // Upload to Google Photos
            await _googlePhotosService.UploadVideoAsync(accessToken, tempPath);

            return Ok(new { message = "Video uploaded successfully" });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = $"Upload failed: {ex.Message}" });
        }
        finally
        {
            // Clean up temporary file
            if (File.Exists(tempPath))
            {
                File.Delete(tempPath);
            }
        }
    }
}

public class CompressVideoRequest
{
    public string Quality { get; set; } = "medium";
}

