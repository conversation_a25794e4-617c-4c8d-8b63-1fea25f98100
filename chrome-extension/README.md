# VidCompressor Chrome Extension

This Chrome extension provides local video compression for Google Photos using WebAssembly (FFmpeg).

## Features

- **Local Compression**: Videos are compressed directly in your browser using WebAssembly
- **Quality Options**: Choose from High, Medium, or Low quality compression
- **Progress Tracking**: Real-time progress updates during compression
- **Upload to Google Photos**: Automatically upload compressed videos back to your library
- **Download Option**: Download compressed videos to your device as a fallback

## Setup Instructions

### 1. Download FFmpeg WebAssembly Library

You need to download the FFmpeg WebAssembly library and place it in the chrome-extension directory:

```bash
cd chrome-extension
curl -o ffmpeg.min.js https://unpkg.com/@ffmpeg/ffmpeg@0.11.6/dist/ffmpeg.min.js
```

### 2. Configure Google OAuth

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google Photos Library API
4. Create OAuth 2.0 credentials for a Chrome extension
5. Update the `client_id` in `manifest.json` with your actual client ID

### 3. Backend Setup

Make sure your backend server is running on `localhost:5000` with the following endpoints:
- `GET /api/videos/{videoId}/info` - Get video information
- `POST /api/videos/upload` - Upload compressed video

### 4. Load Extension

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked" and select the `chrome-extension` directory

## Usage

1. Navigate to Google Photos in Chrome
2. Open any video
3. Click the "Compress Video" button that appears next to the Share button
4. Select your desired quality level
5. Click "Compress" and keep the tab open during processing
6. Choose to upload to Google Photos or download the compressed file

## Technical Details

### Local Compression Workflow

1. **Download**: Video is downloaded directly from Google Photos
2. **Compress**: FFmpeg WebAssembly processes the video locally
3. **Upload**: Compressed video is uploaded back to Google Photos (optional)

### Quality Settings

- **High Quality**: CRF 20, ~30% file size reduction
- **Medium Quality**: CRF 28, ~50% file size reduction  
- **Low Quality**: CRF 35, ~70% file size reduction

### Error Handling

The extension includes comprehensive error handling for:
- Network connectivity issues
- Memory limitations
- Compression failures
- Upload problems

## Files

- `manifest.json` - Extension configuration
- `content.js` - Main content script with UI and compression logic
- `background.js` - Service worker for API communication
- `ffmpeg-helper.js` - WebAssembly compression wrapper
- `popup.html/js` - Extension popup interface

## Browser Compatibility

- Chrome 88+ (required for WebAssembly support)
- Sufficient memory for video processing (varies by video size)

## Troubleshooting

### Common Issues

1. **FFmpeg not loading**: Ensure `ffmpeg.min.js` is downloaded and placed correctly
2. **Authentication errors**: Verify OAuth configuration in Google Cloud Console
3. **Memory errors**: Try using a lower quality setting for large videos
4. **Upload failures**: Check backend server connectivity

### Performance Tips

- Close unnecessary tabs to free up memory
- Use lower quality settings for large videos
- Ensure stable internet connection for upload
