# Chrome Extension Testing Guide

## Quick Setup for Testing (No Backend Required)

### 1. Load the Extension

1. **Open Chrome** and go to `chrome://extensions/`
2. **Enable "Developer mode"** (toggle in top right)
3. **Click "Load unpacked"**
4. **Select the `chrome-extension` folder** from your project

### 2. Test on Google Photos

1. **Go to [photos.google.com](https://photos.google.com)**
2. **Open any video** (click on a video thumbnail)
3. **Look for the "Compress Video" button** next to the Share button
4. **Click it** to test the compression modal

### 3. What to Test

#### ✅ **Basic Functionality**
- [ ] <PERSON><PERSON> appears on video pages
- [ ] <PERSON><PERSON> opens with quality options
- [ ] Quality options show estimated file sizes
- [ ] <PERSON><PERSON> has proper styling (matches Google Photos theme)

#### ✅ **Compression Workflow**
- [ ] Select a quality level (High/Medium/Low)
- [ ] Click "Compress" button
- [ ] Progress modal appears with warning message
- [ ] FFmpeg loads successfully
- [ ] Video downloads and compresses
- [ ] Progress bar updates during compression
- [ ] Download button appears when complete

#### ✅ **Error Handling**
- [ ] Test with very large video (>100MB)
- [ ] Test with unsupported format
- [ ] Test with poor internet connection
- [ ] Retry button works on errors

### 4. Expected Behavior

#### **Compression Modal**
```
┌─────────────────────────────────┐
│ Compress Video                  │
│                                 │
│ ○ High Quality (30% smaller)    │
│ ● Medium Quality (50% smaller)  │
│ ○ Low Quality (70% smaller)     │
│                                 │
│ [Cancel]  [Compress]            │
└─────────────────────────────────┘
```

#### **Progress Modal**
```
┌─────────────────────────────────┐
│ Compressing Video            ×  │
│                                 │
│ ⚠️ Keep this tab open           │
│                                 │
│ Downloading video file...       │
│ ████████████░░░░ 75%           │
│                                 │
│ [Upload to Google Photos]       │
│ [Download File]                 │
└─────────────────────────────────┘
```

### 5. Testing Different Scenarios

#### **Small Video (< 50MB)**
- Should use gentle compression (CRF 20-25)
- Expect 20-40% size reduction
- Fast processing (1-3 minutes)

#### **Medium Video (50-100MB)**
- Should use balanced compression (CRF 25-28)
- Expect 40-60% size reduction
- Moderate processing (3-8 minutes)

#### **Large Video (> 100MB)**
- Should use aggressive compression (CRF 28-30)
- Expect 60-80% size reduction
- Slow processing (8+ minutes)

### 6. Common Issues & Solutions

#### **"FFmpeg library not loaded"**
- Check internet connection
- Try refreshing the page
- Check browser console for errors

#### **"SharedArrayBuffer is not defined"**
- This is expected and handled automatically
- Extension falls back to single-threaded mode

#### **Button doesn't appear**
- Make sure you're on a video page (not photo)
- Try refreshing the page
- Check if extension is enabled

#### **Compression fails**
- Try a smaller video file first
- Check available memory (close other tabs)
- Try a lower quality setting

### 7. Performance Tips

- **Close unnecessary tabs** to free up memory
- **Use lower quality** for large files
- **Ensure stable internet** for video download
- **Keep tab active** during compression

### 8. Debug Information

Open **Developer Tools** (F12) and check:
- **Console tab** for error messages
- **Network tab** for download issues
- **Memory tab** for memory usage

### 9. Test Results to Report

When testing, note:
- ✅ **Video file size** (original vs compressed)
- ✅ **Compression time** (how long it took)
- ✅ **Quality setting** used
- ✅ **Any errors** encountered
- ✅ **Browser performance** during compression

### 10. Next Steps

Once basic compression works:
- Test with different video formats (MP4, MOV, AVI)
- Test with different resolutions (720p, 1080p, 4K)
- Test upload functionality (requires backend setup)
- Test on different devices/browsers

---

## Quick Test Checklist

- [ ] Extension loads without errors
- [ ] Button appears on Google Photos video pages
- [ ] Modal opens and displays correctly
- [ ] Can select quality options
- [ ] Compression starts and shows progress
- [ ] Can download compressed video
- [ ] File size is actually reduced
- [ ] Video plays correctly after compression
