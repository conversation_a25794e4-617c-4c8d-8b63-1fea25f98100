// Test script for local video compression
// This can be run in the browser console to test the compression functionality

async function testCompression() {
  console.log('Testing video compression...');
  
  try {
    // Test if FFmpeg is available
    if (typeof FFmpeg === 'undefined') {
      throw new Error('FFmpeg library not loaded');
    }
    
    console.log('✓ FFmpeg library loaded');
    
    // Test FFmpeg initialization
    const { createFFmpeg } = FFmpeg;
    const ffmpeg = createFFmpeg({
      log: true,
      corePath: 'https://unpkg.com/@ffmpeg/core@0.11.0/dist/ffmpeg-core.js',
    });
    
    console.log('✓ FFmpeg instance created');
    
    // Test loading FFmpeg
    await ffmpeg.load();
    console.log('✓ FFmpeg loaded successfully');
    
    // Test if compression functions are available
    if (typeof compressVideo === 'function') {
      console.log('✓ compressVideo function available');
    } else {
      console.warn('⚠ compressVideo function not found');
    }
    
    // Test if UI functions are available
    if (typeof createCompressionModal === 'function') {
      console.log('✓ createCompressionModal function available');
    } else {
      console.warn('⚠ createCompressionModal function not found');
    }
    
    // Test if background script communication works
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      console.log('✓ Chrome extension API available');
      
      // Test message passing
      chrome.runtime.sendMessage({action: 'test'}, (response) => {
        if (chrome.runtime.lastError) {
          console.warn('⚠ Background script communication test failed:', chrome.runtime.lastError);
        } else {
          console.log('✓ Background script communication working');
        }
      });
    } else {
      console.warn('⚠ Chrome extension API not available');
    }
    
    console.log('✅ All tests passed! Extension should work correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.log('Please check the setup instructions in README.md');
  }
}

// Test quality parameter validation
function testQualityParams() {
  const qualityParams = {
    high: ['-crf', '20', '-preset', 'medium'],
    medium: ['-crf', '28', '-preset', 'medium'],
    low: ['-crf', '35', '-preset', 'medium'],
  };
  
  console.log('Quality parameters:');
  Object.entries(qualityParams).forEach(([quality, params]) => {
    console.log(`  ${quality}: ${params.join(' ')}`);
  });
}

// Test compression ratio calculations
function testCompressionRatios() {
  function getCompressionRatio(quality) {
    switch (quality) {
      case 'high': return 0.7;   // 30% reduction
      case 'medium': return 0.5; // 50% reduction
      case 'low': return 0.3;    // 70% reduction
      default: return 0.5;
    }
  }
  
  const originalSize = 100 * 1024 * 1024; // 100MB
  
  console.log('Compression ratio test (100MB original):');
  ['high', 'medium', 'low'].forEach(quality => {
    const ratio = getCompressionRatio(quality);
    const compressedSize = originalSize * ratio;
    const reduction = Math.round((1 - ratio) * 100);
    console.log(`  ${quality}: ${(compressedSize / 1024 / 1024).toFixed(1)}MB (${reduction}% reduction)`);
  });
}

// Run tests
console.log('VidCompressor Extension Test Suite');
console.log('==================================');
testQualityParams();
testCompressionRatios();
testCompression();
