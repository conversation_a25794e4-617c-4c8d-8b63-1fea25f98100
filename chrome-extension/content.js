// Load FFmpeg dynamically
let ffmpegLoaded = false;
let ffmpegLoadPromise = null;

async function loadFFmpegLibrary() {
  if (ffmpegLoaded) return;
  if (ffmpegLoadPromise) return ffmpegLoadPromise;

  ffmpegLoadPromise = new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = 'https://unpkg.com/@ffmpeg/ffmpeg@0.10.1/dist/ffmpeg.min.js';
    script.onload = () => {
      ffmpegLoaded = true;
      resolve();
    };
    script.onerror = () => reject(new Error('Failed to load FFmpeg library'));
    document.head.appendChild(script);
  });

  return ffmpegLoadPromise;
}

// FFmpeg compression functionality
let ffmpeg;

async function loadFFmpeg() {
  try {
    await loadFFmpegLibrary();

    if (!ffmpeg) {
      const { createFFmpeg } = window.FFmpeg;
      ffmpeg = createFFmpeg({
        log: true,
        corePath: 'https://unpkg.com/@ffmpeg/core@0.10.0/dist/ffmpeg-core.js',
      });
    }
    if (!ffmpeg.isLoaded()) {
      await ffmpeg.load();
    }
    return ffmpeg;
  } catch (error) {
    console.error('Failed to load FFmpeg:', error);
    throw new Error('Failed to load video compression engine. Please check your internet connection and try again.');
  }
}

async function compressVideo(videoUrl, quality, progressCallback) {
  try {
    const ffmpeg = await loadFFmpeg();

    ffmpeg.setLogger(({ type, message }) => {
      if (type === 'fferr') {
        progressCallback({ type, message });
      }
    });

    ffmpeg.setProgress(({ ratio }) => {
      progressCallback({ type: 'progress', ratio, message: `Compressing... ${Math.round(ratio * 100)}%` });
    });

    const inputFileName = 'input.mp4';
    const outputFileName = 'output.mp4';

    progressCallback({ type: 'status', message: 'Downloading video file...' });

    let fileData;
    try {
      const { fetchFile } = window.FFmpeg;
      fileData = await fetchFile(videoUrl);
    } catch (error) {
      throw new Error('Failed to download video file. Please check your internet connection.');
    }

    if (!fileData || fileData.length === 0) {
      throw new Error('Downloaded video file is empty or corrupted.');
    }

    ffmpeg.FS('writeFile', inputFileName, fileData);

    const qualityParams = {
      high: ['-crf', '20', '-preset', 'medium', '-maxrate', '4000k', '-bufsize', '8000k'],
      medium: ['-crf', '25', '-preset', 'medium', '-maxrate', '2500k', '-bufsize', '5000k'],
      low: ['-crf', '30', '-preset', 'medium', '-maxrate', '1500k', '-bufsize', '3000k'],
    };

    const params = qualityParams[quality] || qualityParams.medium;

    progressCallback({ type: 'status', message: 'Starting compression...' });

    try {
      await ffmpeg.run(
        '-i', inputFileName,
        '-c:v', 'libx264',
        ...params,
        '-movflags', '+faststart',
        '-pix_fmt', 'yuv420p',
        '-c:a', 'aac',
        '-b:a', '128k',
        '-avoid_negative_ts', 'make_zero',
        outputFileName
      );
    } catch (error) {
      throw new Error('Video compression failed. The video format may not be supported or the file may be corrupted.');
    }

    progressCallback({ type: 'status', message: 'Reading compressed file...' });

    let data;
    try {
      data = ffmpeg.FS('readFile', outputFileName);
    } catch (error) {
      throw new Error('Failed to read compressed video file.');
    }

    // Clean up files
    try {
      ffmpeg.FS('unlink', inputFileName);
      ffmpeg.FS('unlink', outputFileName);
    } catch (error) {
      console.warn('Failed to clean up temporary files:', error);
    }

    if (!data || data.length === 0) {
      throw new Error('Compressed video file is empty. Compression may have failed.');
    }

    return new Blob([data.buffer], { type: 'video/mp4' });

  } catch (error) {
    console.error('Compression error:', error);
    throw error;
  }
}

// Utility functions for size formatting
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getCompressionRatio(quality) {
  // Estimated compression ratios based on CRF values
  switch (quality) {
    case 'high': return 0.7;   // 30% reduction
    case 'medium': return 0.5; // 50% reduction
    case 'low': return 0.3;    // 70% reduction
    default: return 0.5;
  }
}

// Theme detection utility
function getThemeColors() {
  const isDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

  if (isDarkMode) {
    return {
      background: '#202124',
      surface: '#303134',
      onSurface: '#e8eaed',
      onSurfaceVariant: '#9aa0a6',
      primary: '#8ab4f8',
      primaryHover: '#aecbfa',
      border: '#5f6368',
      borderHover: '#80868b',
      borderSelected: '#8ab4f8',
      surfaceSelected: '#1e3a8a',
      surfaceHover: '#3c4043',
      overlay: 'rgba(0, 0, 0, 0.8)',
      spinnerTrack: '#5f6368',
      spinnerActive: '#8ab4f8'
    };
  } else {
    return {
      background: '#fff',
      surface: '#fff',
      onSurface: '#202124',
      onSurfaceVariant: '#5f6368',
      primary: '#1a73e8',
      primaryHover: '#1557b0',
      border: '#e8eaed',
      borderHover: '#dadce0',
      borderSelected: '#1a73e8',
      surfaceSelected: '#e8f0fe',
      surfaceHover: '#f8f9fa',
      overlay: 'rgba(0, 0, 0, 0.6)',
      spinnerTrack: '#f3f3f3',
      spinnerActive: '#1a73e8'
    };
  }
}

// Loading modal functionality
function createLoadingModal() {
  const theme = getThemeColors();
  const modal = document.createElement('div');
  modal.id = 'loading-modal';
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: ${theme.overlay};
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Google Sans', Roboto, Arial, sans-serif;
  `;

  const loadingContent = document.createElement('div');
  loadingContent.style.cssText = `
    background: ${theme.surface};
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 8px 10px 1px rgba(0,0,0,0.14), 0 3px 14px 2px rgba(0,0,0,0.12), 0 5px 5px -3px rgba(0,0,0,0.2);
  `;

  const spinner = document.createElement('div');
  spinner.style.cssText = `
    width: 32px;
    height: 32px;
    border: 3px solid ${theme.spinnerTrack};
    border-top: 3px solid ${theme.spinnerActive};
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px auto;
  `;

  const loadingText = document.createElement('div');
  loadingText.textContent = 'Getting video information...';
  loadingText.style.cssText = `
    color: ${theme.onSurfaceVariant};
    font-size: 14px;
  `;

  // Add CSS animation for spinner
  if (!document.getElementById('spinner-style')) {
    const style = document.createElement('style');
    style.id = 'spinner-style';
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
  }

  loadingContent.appendChild(spinner);
  loadingContent.appendChild(loadingText);
  modal.appendChild(loadingContent);

  // Listen for theme changes and update modal accordingly
  const themeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  const handleThemeChange = () => {
    // Remove and recreate modal with new theme
    if (document.getElementById('loading-modal')) {
      modal.remove();
      const newModal = createLoadingModal();
      document.body.appendChild(newModal);
    }
  };
  themeMediaQuery.addEventListener('change', handleThemeChange);

  // Clean up listener when modal is removed
  const originalRemove = modal.remove.bind(modal);
  modal.remove = () => {
    themeMediaQuery.removeEventListener('change', handleThemeChange);
    originalRemove();
  };

  return modal;
}

// Compression progress modal
function createCompressionProgressModal(videoInfo) {
  const theme = getThemeColors();
  const modal = document.createElement('div');
  modal.id = 'compression-progress-modal';
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: ${theme.overlay};
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Google Sans', Roboto, Arial, sans-serif;
  `;

  const modalContent = document.createElement('div');
  modalContent.style.cssText = `
    background: ${theme.surface};
    border-radius: 8px;
    width: 400px;
    max-width: 90vw;
    padding: 24px;
    box-shadow: 0 8px 10px 1px rgba(0,0,0,0.14), 0 3px 14px 2px rgba(0,0,0,0.12), 0 5px 5px -3px rgba(0,0,0,0.2);
  `;

  const title = document.createElement('h2');
  title.textContent = 'Compressing Video';
  title.style.cssText = `
    margin: 0 0 16px 0;
    font-size: 20px;
    font-weight: 500;
    color: ${theme.onSurface};
  `;

  const statusText = document.createElement('p');
  statusText.id = 'compression-status';
  statusText.textContent = 'Initializing...';
  statusText.style.cssText = `
    margin: 0 0 12px 0;
    color: ${theme.onSurfaceVariant};
    font-size: 14px;
    min-height: 20px;
  `;

  // Warning message
  const warningText = document.createElement('div');
  warningText.innerHTML = '⚠️ <strong>Keep this tab open</strong> - Compression is happening on your computer. This may be slow for large files.';
  warningText.style.cssText = `
    margin: 0 0 16px 0;
    padding: 12px;
    background: ${theme.surface === '#ffffff' ? '#fff3cd' : '#3c3000'};
    border: 1px solid ${theme.surface === '#ffffff' ? '#ffeaa7' : '#6c5700'};
    border-radius: 4px;
    color: ${theme.surface === '#ffffff' ? '#856404' : '#ffeb3b'};
    font-size: 13px;
    line-height: 1.4;
  `;

  const progressBarContainer = document.createElement('div');
  progressBarContainer.style.cssText = `
    width: 100%;
    background-color: ${theme.spinnerTrack};
    border-radius: 4px;
    overflow: hidden;
    height: 8px;
  `;

  const progressBar = document.createElement('div');
  progressBar.id = 'compression-progress-bar';
  progressBar.style.cssText = `
    width: 0%;
    height: 8px;
    background-color: ${theme.primary};
    transition: width 0.2s;
  `;
  progressBarContainer.appendChild(progressBar);

  const downloadContainer = document.createElement('div');
  downloadContainer.id = 'download-container';
  downloadContainer.style.cssText = 'margin-top: 20px; text-align: center;';

  // Close button (initially hidden)
  const closeButton = document.createElement('button');
  closeButton.textContent = '×';
  closeButton.style.cssText = `
    position: absolute;
    top: 12px;
    right: 12px;
    background: none;
    border: none;
    font-size: 24px;
    color: ${theme.onSurfaceVariant};
    cursor: pointer;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: none;
  `;
  closeButton.onmouseover = () => closeButton.style.backgroundColor = theme.spinnerTrack;
  closeButton.onmouseout = () => closeButton.style.backgroundColor = 'transparent';
  closeButton.onclick = () => modal.remove();

  modalContent.style.position = 'relative';
  modalContent.appendChild(closeButton);
  modalContent.appendChild(title);
  modalContent.appendChild(warningText);
  modalContent.appendChild(statusText);
  modalContent.appendChild(progressBarContainer);
  modalContent.appendChild(downloadContainer);
  modal.appendChild(modalContent);

  modal.updateProgress = (progress) => {
    if (progress.type === 'status') {
      statusText.textContent = progress.message;
      // Hide warning when compression starts
      if (progress.message.includes('Starting compression') || progress.message.includes('Compressing')) {
        warningText.style.display = 'none';
      }
    } else if (progress.type === 'progress') {
      const percent = Math.round(progress.ratio * 100);
      progressBar.style.width = `${percent}%`;
      statusText.textContent = `Compressing... ${percent}%`;
      // Hide warning during active compression
      warningText.style.display = 'none';
    }
  };

  modal.showDownload = (blob, filename) => {
    progressBar.style.width = '100%';
    statusText.textContent = 'Compression complete!';
    closeButton.style.display = 'block'; // Show close button when compression is done

    const buttonContainer = document.createElement('div');
    buttonContainer.style.cssText = `
      display: flex;
      gap: 12px;
      justify-content: center;
      flex-wrap: wrap;
    `;

    // Upload to Google Photos button (disabled for testing)
    const uploadButton = document.createElement('button');
    uploadButton.textContent = `Upload to Google Photos (${formatFileSize(blob.size)}) - Coming Soon`;
    uploadButton.style.cssText = `
      padding: 10px 24px;
      border: none;
      background: ${theme.onSurfaceVariant};
      color: white;
      font-weight: 500;
      border-radius: 4px;
      cursor: not-allowed;
      font-size: 14px;
      opacity: 0.6;
    `;
    uploadButton.disabled = true;

    uploadButton.onclick = () => {
      alert('Upload functionality is disabled for testing. Use the download button instead!');
    };

    // Download link (as fallback)
    const downloadLink = document.createElement('a');
    downloadLink.href = URL.createObjectURL(blob);
    const compressedFilename = filename.replace(/(\.[^/.]+$)/, '') + '-compressed.mp4';
    downloadLink.download = compressedFilename;
    downloadLink.textContent = 'Download File';
    downloadLink.style.cssText = `
      display: inline-block;
      padding: 10px 24px;
      border: 2px solid ${theme.primary};
      background: transparent;
      color: ${theme.primary};
      font-weight: 500;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      text-decoration: none;
    `;
    downloadLink.onmouseover = () => {
      downloadLink.style.backgroundColor = theme.primary;
      downloadLink.style.color = 'white';
    };
    downloadLink.onmouseout = () => {
      downloadLink.style.backgroundColor = 'transparent';
      downloadLink.style.color = theme.primary;
    };

    buttonContainer.appendChild(uploadButton);
    buttonContainer.appendChild(downloadLink);
    downloadContainer.appendChild(buttonContainer);
  };

  modal.showError = (message) => {
    statusText.textContent = message;
    progressBar.style.backgroundColor = '#d93025'; // Red color for error
    progressBar.style.width = '100%';
    closeButton.style.display = 'block'; // Show close button on error

    // Add retry button for errors
    const retryButton = document.createElement('button');
    retryButton.textContent = 'Try Again';
    retryButton.style.cssText = `
      margin-top: 16px;
      padding: 10px 24px;
      border: none;
      background: ${theme.primary};
      color: white;
      font-weight: 500;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    `;
    retryButton.onmouseover = () => retryButton.style.backgroundColor = theme.primaryHover;
    retryButton.onmouseout = () => retryButton.style.backgroundColor = theme.primary;
    retryButton.onclick = () => {
      modal.remove();
      // Trigger the compression modal again
      const videoId = window.location.pathname.split('/')[2];
      if (videoId) {
        document.getElementById('compress-button')?.click();
      }
    };

    downloadContainer.appendChild(retryButton);
  };

  return modal;
}


// Compression modal functionality
function createCompressionModal(videoInfo) {
  const theme = getThemeColors();
  const modal = document.createElement('div');
  modal.id = 'compression-modal';
  modal.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: ${theme.overlay};
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Google Sans', Roboto, Arial, sans-serif;
  `;

  const modalContent = document.createElement('div');
  modalContent.style.cssText = `
    background: ${theme.surface};
    border-radius: 8px;
    width: 400px;
    max-width: 90vw;
    box-shadow: 0 8px 10px 1px rgba(0,0,0,0.14), 0 3px 14px 2px rgba(0,0,0,0.12), 0 5px 5px -3px rgba(0,0,0,0.2);
    overflow: hidden;
  `;

  const header = document.createElement('div');
  header.style.cssText = `
    padding: 24px 24px 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  `;

  const title = document.createElement('h2');
  title.textContent = 'Compress Video';
  title.style.cssText = `
    margin: 0;
    font-size: 20px;
    font-weight: 500;
    color: ${theme.onSurface};
    display: flex;
    align-items: center;
  `;

  const closeButton = document.createElement('button');
  closeButton.innerHTML = '×';
  closeButton.style.cssText = `
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: ${theme.onSurfaceVariant};
    padding: 8px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  `;
  closeButton.onmouseover = () => closeButton.style.background = theme.surfaceHover;
  closeButton.onmouseout = () => closeButton.style.background = 'none';

  header.appendChild(title);
  header.appendChild(closeButton);

  const content = document.createElement('div');
  content.style.cssText = `
    padding: 24px;
  `;

  const videoDetails = document.createElement('div');
  if (videoInfo) {
    videoDetails.innerHTML = `
      <div style="margin-bottom: 8px; color: ${theme.onSurface}; font-size: 14px; font-weight: 500;">
        ${videoInfo.filename}
      </div>
      <div style="margin-bottom: 16px; color: ${theme.onSurfaceVariant}; font-size: 12px;">
        ${videoInfo.width}×${videoInfo.height} • ${Math.round(videoInfo.duration)}s • ${formatFileSize(videoInfo.estimatedSizeBytes)}
      </div>
    `;
  }

  const description = document.createElement('p');
  description.textContent = 'Choose compression quality:';
  description.style.cssText = `
    margin: 0 0 20px 0;
    color: ${theme.onSurfaceVariant};
    font-size: 14px;
  `;

  const optionsContainer = document.createElement('div');
  optionsContainer.style.cssText = `
    display: flex;
    flex-direction: column;
    gap: 12px;
  `;

  const originalSize = videoInfo ? videoInfo.estimatedSizeBytes : 0;
  const compressionOptions = [
    {
      value: 'high',
      label: 'High Quality',
      description: 'Minimal compression, larger file size',
      estimatedSize: originalSize * getCompressionRatio('high')
    },
    {
      value: 'medium',
      label: 'Medium Quality',
      description: 'Balanced compression and quality',
      estimatedSize: originalSize * getCompressionRatio('medium')
    },
    {
      value: 'low',
      label: 'Low Quality',
      description: 'Maximum compression, smaller file size',
      estimatedSize: originalSize * getCompressionRatio('low')
    }
  ];

  let selectedOption = 'medium';

  compressionOptions.forEach(option => {
    const optionDiv = document.createElement('div');
    optionDiv.style.cssText = `
      padding: 16px;
      border: 2px solid ${theme.border};
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
    `;

    const radio = document.createElement('input');
    radio.type = 'radio';
    radio.name = 'compression';
    radio.value = option.value;
    radio.checked = option.value === selectedOption;
    radio.style.cssText = `
      margin-right: 12px;
      accent-color: ${theme.primary};
    `;

    const labelContainer = document.createElement('div');
    labelContainer.style.cssText = `
      flex: 1;
    `;

    const label = document.createElement('div');
    label.textContent = option.label;
    label.style.cssText = `
      font-weight: 500;
      color: ${theme.onSurface};
      margin-bottom: 4px;
    `;

    const desc = document.createElement('div');
    desc.textContent = option.description;
    desc.style.cssText = `
      font-size: 12px;
      color: ${theme.onSurfaceVariant};
      margin-bottom: 2px;
    `;

    const sizeInfo = document.createElement('div');
    if (videoInfo && option.estimatedSize > 0) {
      const savings = originalSize - option.estimatedSize;
      const savingsPercent = Math.round((savings / originalSize) * 100);
      sizeInfo.textContent = `Est. ${formatFileSize(option.estimatedSize)} (${savingsPercent}% smaller)`;
    } else {
      sizeInfo.textContent = 'Size estimate unavailable';
    }
    sizeInfo.style.cssText = `
      font-size: 11px;
      color: ${theme.primary};
      font-weight: 500;
    `;

    labelContainer.appendChild(label);
    labelContainer.appendChild(desc);
    labelContainer.appendChild(sizeInfo);
    optionDiv.appendChild(radio);
    optionDiv.appendChild(labelContainer);

    optionDiv.onclick = () => {
      selectedOption = option.value;
      document.querySelectorAll('input[name="compression"]').forEach(r => r.checked = false);
      radio.checked = true;
      updateOptionStyles();
    };

    optionDiv.onmouseover = () => {
      if (!radio.checked) {
        optionDiv.style.borderColor = theme.borderHover;
        optionDiv.style.backgroundColor = theme.surfaceHover;
      }
    };

    optionDiv.onmouseout = () => {
      if (!radio.checked) {
        optionDiv.style.borderColor = theme.border;
        optionDiv.style.backgroundColor = 'transparent';
      }
    };

    optionsContainer.appendChild(optionDiv);
  });

  function updateOptionStyles() {
    document.querySelectorAll('#compression-modal [name="compression"]').forEach(radio => {
      const optionDiv = radio.closest('div');
      if (radio.checked) {
        optionDiv.style.borderColor = theme.borderSelected;
        optionDiv.style.backgroundColor = theme.surfaceSelected;
      } else {
        optionDiv.style.borderColor = theme.border;
        optionDiv.style.backgroundColor = 'transparent';
      }
    });
  }

  const buttonContainer = document.createElement('div');
  buttonContainer.style.cssText = `
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 24px;
  `;

  const cancelButton = document.createElement('button');
  cancelButton.textContent = 'Cancel';
  cancelButton.style.cssText = `
    padding: 10px 24px;
    border: none;
    background: none;
    color: ${theme.primary};
    font-weight: 500;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
  `;
  cancelButton.onmouseover = () => cancelButton.style.backgroundColor = theme.surfaceHover;
  cancelButton.onmouseout = () => cancelButton.style.backgroundColor = 'transparent';

  const compressButton = document.createElement('button');
  compressButton.textContent = 'Compress';
  compressButton.style.cssText = `
    padding: 10px 24px;
    border: none;
    background: ${theme.primary};
    color: white;
    font-weight: 500;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
  `;
  compressButton.onmouseover = () => compressButton.style.backgroundColor = theme.primaryHover;
  compressButton.onmouseout = () => compressButton.style.backgroundColor = theme.primary;

  buttonContainer.appendChild(cancelButton);
  buttonContainer.appendChild(compressButton);

  if (videoInfo) {
    content.appendChild(videoDetails);
  }
  content.appendChild(description);
  content.appendChild(optionsContainer);
  content.appendChild(buttonContainer);

  modalContent.appendChild(header);
  modalContent.appendChild(content);
  modal.appendChild(modalContent);

  // Event handlers
  const closeModal = () => {
    modal.remove();
  };

  closeButton.onclick = closeModal;
  cancelButton.onclick = closeModal;
  modal.onclick = (e) => {
    if (e.target === modal) closeModal();
  };

  compressButton.onclick = async () => {
    const videoId = window.location.pathname.split('/')[2];
    if (!videoId || !videoInfo || !videoInfo.baseUrl) {
      alert('Could not get video information for compression.');
      return;
    }

    const selectedQuality = selectedOption;
    closeModal();

    const progressModal = createCompressionProgressModal(videoInfo);
    document.body.appendChild(progressModal);

    try {
      const compressedBlob = await compressVideo(
        videoInfo.baseUrl + '=dv', // Parameter for direct video download
        selectedQuality,
        (progress) => {
          progressModal.updateProgress(progress);
        }
      );

      progressModal.showDownload(compressedBlob, videoInfo.filename);

    } catch (error) {
      console.error('Compression failed:', error);
      let errorMessage = 'Compression failed. ';

      if (error.message.includes('network') || error.message.includes('fetch')) {
        errorMessage += 'Network error - please check your connection and try again.';
      } else if (error.message.includes('memory') || error.message.includes('out of memory')) {
        errorMessage += 'Not enough memory - try closing other tabs or using a lower quality setting.';
      } else if (error.message.includes('timeout')) {
        errorMessage += 'Operation timed out - the video may be too large. Try a lower quality setting.';
      } else {
        errorMessage += 'Please try again or check the console for details.';
      }

      progressModal.showError(errorMessage);
    }
  };

  // Initialize styles
  setTimeout(updateOptionStyles, 0);

  // Listen for theme changes and update modal accordingly
  const themeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  const handleThemeChange = () => {
    // Remove and recreate modal with new theme
    if (document.getElementById('compression-modal')) {
      modal.remove();
      const newModal = createCompressionModal(videoInfo);
      document.body.appendChild(newModal);
    }
  };
  themeMediaQuery.addEventListener('change', handleThemeChange);

  // Clean up listener when modal is removed
  const originalRemove = modal.remove.bind(modal);
  modal.remove = () => {
    themeMediaQuery.removeEventListener('change', handleThemeChange);
    originalRemove();
  };

  return modal;
}

function addButton() {
  if (document.getElementById('compress-button')) {
    return;
  }

  const shareButton = document.querySelector('button[aria-label="Share"]');
  if (!shareButton) {
    setTimeout(addButton, 500); // Try again if the share button isn't loaded yet
    return;
  }

  const shareButtonWrapper = shareButton.closest('.DNAsC.G6iPcb');
  if (!shareButtonWrapper) {
    return;
  }

  const newButtonWrapper = document.createElement('div');
  newButtonWrapper.className = shareButtonWrapper.className;
  newButtonWrapper.setAttribute('jscontroller', shareButtonWrapper.getAttribute('jscontroller'));
  newButtonWrapper.setAttribute('jsaction', shareButtonWrapper.getAttribute('jsaction'));

  const tooltipWrapper = document.createElement('span');
  tooltipWrapper.setAttribute('data-is-tooltip-wrapper', 'true');

  const button = document.createElement('button');
  button.id = 'compress-button';
  button.className = shareButton.className;
  button.setAttribute('aria-label', 'Compress');
  button.setAttribute('data-tooltip-enabled', 'true');
  const tooltipId = 'tt-compress-' + Date.now();
  button.setAttribute('data-tooltip-id', tooltipId);
  button.setAttribute('data-tooltip-classes', 'hoXpM'); // Added missing attribute
  button.setAttribute('jscontroller', shareButton.getAttribute('jscontroller'));
  button.setAttribute('jsaction', shareButton.getAttribute('jsaction'));

  button.innerHTML = `
    <span class="OiePBf-zPjgPe pYTkkf-Bz112c-UHGRz"></span>
    <span class="RBHQF-ksKsZd"></span>
    <span class="pYTkkf-Bz112c-kBDsod-Rtc0Jf" aria-hidden="true">
      <span class="notranslate" aria-hidden="true">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15 9L20 4M20 4H16M20 4V8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M9 9L4 4M4 4H8M4 4V8" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M15 15L20 20M20 20H16M20 20V16" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M9 15L4 20M4 20H8M4 20V16" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </span>
    </span>
    <div class="pYTkkf-Bz112c-RLmnJb"></div>
  `;

  const tooltip = document.createElement('div');
  tooltip.className = 'ne2Ple-oshW8e-V67aGc';
  tooltip.id = tooltipId;
  tooltip.setAttribute('role', 'tooltip');
  tooltip.setAttribute('aria-hidden', 'true');
  tooltip.textContent = 'Compress';

  tooltipWrapper.appendChild(button);
  tooltipWrapper.appendChild(tooltip);
  newButtonWrapper.appendChild(tooltipWrapper);

  shareButtonWrapper.parentElement.insertBefore(newButtonWrapper, shareButtonWrapper);

  button.onclick = async (e) => {
    e.stopPropagation();
    const videoId = window.location.pathname.split('/')[2];
    if (!videoId) return;

    // Show loading modal first
    const loadingModal = createLoadingModal();
    document.body.appendChild(loadingModal);

    try {
      // Fetch video info
      const response = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage({
          action: "getVideoInfo",
          videoId: videoId
        }, function(response) {
          if (response.success) {
            resolve(response);
          } else {
            reject(new Error(response.message));
          }
        });
      });

      // Remove loading modal and show compression modal with video info
      loadingModal.remove();
      const modal = createCompressionModal(response.videoInfo);
      document.body.appendChild(modal);
    } catch (error) {
      // Remove loading modal and show compression modal without video info
      loadingModal.remove();
      const modal = createCompressionModal(null);
      document.body.appendChild(modal);
      console.error('Failed to fetch video info:', error);
    }
  };
}

const observer = new MutationObserver(() => {
  addButton();
});

observer.observe(document.body, {
  childList: true,
  subtree: true
});

addButton();
