<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VidCompressor Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1a73e8;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1557b0;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { color: #137333; }
        .error { color: #d93025; }
        .warning { color: #ea8600; }
        .info { color: #1a73e8; }
        input[type="file"] {
            margin: 10px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #1a73e8;
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 VidCompressor Test Suite</h1>
        
        <div class="test-section">
            <h3>1. Library Loading Test</h3>
            <button onclick="testLibraryLoading()">Test FFmpeg Loading</button>
            <div id="library-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>2. Compression Functions Test</h3>
            <button onclick="testCompressionFunctions()">Test Functions</button>
            <div id="functions-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>3. UI Components Test</h3>
            <button onclick="testUIComponents()">Test Modal UI</button>
            <button onclick="testProgressModal()">Test Progress Modal</button>
            <div id="ui-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>4. Video Compression Test</h3>
            <input type="file" id="videoFile" accept="video/*">
            <br>
            <button onclick="testVideoCompression()" id="compressBtn">Compress Test Video</button>
            <div class="progress-bar" style="display: none;" id="progressBar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="compression-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>5. Quality Settings Test</h3>
            <button onclick="testQualitySettings()">Test Quality Parameters</button>
            <div id="quality-log" class="log"></div>
        </div>
    </div>

    <!-- Load FFmpeg -->
    <script src="ffmpeg.min.js"></script>
    <script src="ffmpeg-helper.js"></script>
    
    <script>
        function log(elementId, message, type = 'info') {
            const logElement = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        async function testLibraryLoading() {
            const logId = 'library-log';
            log(logId, 'Testing FFmpeg library loading...', 'info');
            
            try {
                if (typeof FFmpeg === 'undefined') {
                    throw new Error('FFmpeg library not loaded');
                }
                log(logId, '✓ FFmpeg library loaded', 'success');
                
                const { createFFmpeg } = FFmpeg;
                const ffmpeg = createFFmpeg({
                    log: true,
                    corePath: 'https://unpkg.com/@ffmpeg/core-st@0.11.1/dist/ffmpeg-core.js',
                    wasmPath: 'https://unpkg.com/@ffmpeg/core-st@0.11.1/dist/ffmpeg-core.wasm',
                    workerPath: 'https://unpkg.com/@ffmpeg/core-st@0.11.1/dist/ffmpeg-core.worker.js',
                });
                log(logId, '✓ FFmpeg instance created', 'success');
                
                log(logId, 'Loading FFmpeg core (this may take a moment)...', 'info');
                await ffmpeg.load();
                log(logId, '✓ FFmpeg core loaded successfully!', 'success');
                
            } catch (error) {
                log(logId, `❌ Error: ${error.message}`, 'error');
            }
        }

        function testCompressionFunctions() {
            const logId = 'functions-log';
            log(logId, 'Testing compression functions...', 'info');
            
            if (typeof compressVideo === 'function') {
                log(logId, '✓ compressVideo function available', 'success');
            } else {
                log(logId, '⚠ compressVideo function not found', 'warning');
            }
            
            if (typeof loadFFmpeg === 'function') {
                log(logId, '✓ loadFFmpeg function available', 'success');
            } else {
                log(logId, '⚠ loadFFmpeg function not found', 'warning');
            }
        }

        function testUIComponents() {
            const logId = 'ui-log';
            log(logId, 'Testing UI components...', 'info');
            
            // Test modal creation (simplified version)
            try {
                const modal = document.createElement('div');
                modal.style.cssText = `
                    position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                    background: white; padding: 20px; border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3); z-index: 10000;
                `;
                modal.innerHTML = '<h3>Test Modal</h3><p>This is a test modal</p><button onclick="this.parentElement.remove()">Close</button>';
                document.body.appendChild(modal);
                log(logId, '✓ Modal created successfully', 'success');
                
                setTimeout(() => {
                    if (document.body.contains(modal)) {
                        modal.remove();
                        log(logId, '✓ Modal auto-removed', 'success');
                    }
                }, 3000);
                
            } catch (error) {
                log(logId, `❌ Modal creation failed: ${error.message}`, 'error');
            }
        }

        function testProgressModal() {
            const logId = 'ui-log';
            log(logId, 'Testing progress modal...', 'info');
            
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            
            progressBar.style.display = 'block';
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                progressFill.style.width = progress + '%';
                log(logId, `Progress: ${progress}%`, 'info');
                
                if (progress >= 100) {
                    clearInterval(interval);
                    log(logId, '✓ Progress test completed', 'success');
                    setTimeout(() => {
                        progressBar.style.display = 'none';
                        progressFill.style.width = '0%';
                    }, 1000);
                }
            }, 200);
        }

        async function testVideoCompression() {
            const logId = 'compression-log';
            const fileInput = document.getElementById('videoFile');
            const compressBtn = document.getElementById('compressBtn');
            
            if (!fileInput.files[0]) {
                log(logId, '⚠ Please select a video file first', 'warning');
                return;
            }
            
            const file = fileInput.files[0];
            log(logId, `Testing compression with file: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`, 'info');
            
            compressBtn.disabled = true;
            
            try {
                if (typeof compressVideo !== 'function') {
                    throw new Error('compressVideo function not available');
                }
                
                // Create a blob URL for the file
                const videoUrl = URL.createObjectURL(file);
                
                const progressCallback = (progress) => {
                    if (progress.type === 'status') {
                        log(logId, progress.message, 'info');
                    } else if (progress.type === 'progress') {
                        const percent = Math.round(progress.ratio * 100);
                        log(logId, `Compression progress: ${percent}%`, 'info');
                    }
                };
                
                log(logId, 'Starting compression...', 'info');
                const compressedBlob = await compressVideo(videoUrl, 'medium', progressCallback);
                
                const originalSize = file.size;
                const compressedSize = compressedBlob.size;
                const reduction = Math.round((1 - compressedSize / originalSize) * 100);
                
                log(logId, `✓ Compression completed!`, 'success');
                log(logId, `Original: ${(originalSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                log(logId, `Compressed: ${(compressedSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                log(logId, `Reduction: ${reduction}%`, 'success');
                
                // Create download link
                const downloadLink = document.createElement('a');
                downloadLink.href = URL.createObjectURL(compressedBlob);
                downloadLink.download = 'compressed-' + file.name;
                downloadLink.textContent = 'Download Compressed Video';
                downloadLink.style.cssText = 'display: block; margin: 10px 0; color: #1a73e8;';
                document.getElementById('compression-log').appendChild(downloadLink);
                
                URL.revokeObjectURL(videoUrl);
                
            } catch (error) {
                log(logId, `❌ Compression failed: ${error.message}`, 'error');
            } finally {
                compressBtn.disabled = false;
            }
        }

        function testQualitySettings() {
            const logId = 'quality-log';
            log(logId, 'Testing quality settings...', 'info');
            
            const qualityParams = {
                high: ['-crf', '20', '-preset', 'medium'],
                medium: ['-crf', '28', '-preset', 'medium'],
                low: ['-crf', '35', '-preset', 'medium'],
            };
            
            log(logId, 'Quality parameters:', 'info');
            Object.entries(qualityParams).forEach(([quality, params]) => {
                log(logId, `  ${quality}: ${params.join(' ')}`, 'info');
            });
            
            // Test compression ratios
            const originalSize = 100 * 1024 * 1024; // 100MB
            log(logId, 'Estimated compression ratios (100MB original):', 'info');
            
            const ratios = { high: 0.7, medium: 0.5, low: 0.3 };
            Object.entries(ratios).forEach(([quality, ratio]) => {
                const compressedSize = originalSize * ratio;
                const reduction = Math.round((1 - ratio) * 100);
                log(logId, `  ${quality}: ${(compressedSize / 1024 / 1024).toFixed(1)}MB (${reduction}% reduction)`, 'success');
            });
        }

        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('library-log', 'Page loaded. Ready for testing!', 'info');
            }, 500);
        });
    </script>
</body>
</html>
