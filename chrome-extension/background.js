chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "getVideoInfo") {
    chrome.identity.getAuthToken({ interactive: true }, function(token) {
      if (chrome.runtime.lastError) {
        console.error(chrome.runtime.lastError);
        sendResponse({ success: false, message: "Could not authenticate." });
        return;
      }

      fetch(`http://localhost:5000/api/videos/${request.videoId}/info`, {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer ' + token,
          'Content-Type': 'application/json'
        }
      })
      .then(response => response.json())
      .then(data => {
        sendResponse({ success: true, videoInfo: data });
      })
      .catch(error => {
        console.error('Error:', error);
        sendResponse({ success: false, message: 'Error fetching video info: ' + error.message });
      });
    });
    return true; // Indicates that the response is sent asynchronously
  }

  if (request.action === "uploadVideo") {
    chrome.identity.getAuthToken({ interactive: true }, function(token) {
      if (chrome.runtime.lastError) {
        console.error(chrome.runtime.lastError);
        sendResponse({ success: false, message: "Could not authenticate." });
        return;
      }

      const formData = new FormData();
      formData.append('videoFile', request.videoBlob, request.filename || 'compressed-video.mp4');

      fetch('http://localhost:5000/api/videos/upload', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + token
        },
        body: formData
      })
      .then(response => response.json())
      .then(data => {
        sendResponse({ success: true, message: data.message });
      })
      .catch(error => {
        console.error('Error:', error);
        sendResponse({ success: false, message: 'Error uploading video: ' + error.message });
      });
    });

    return true; // Indicates that the response is sent asynchronously
  }
});
