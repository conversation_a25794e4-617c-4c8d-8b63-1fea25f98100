<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple FFmpeg Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #1a73e8; text-align: center; }
        button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        button:hover { background: #1557b0; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .success { color: #137333; }
        .error { color: #d93025; }
        .info { color: #1a73e8; }
        input[type="file"] { margin: 10px 0; }
        .progress {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background: #1a73e8;
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Simple FFmpeg Test</h1>
        
        <div>
            <button onclick="testBasicFFmpeg()">Test Basic FFmpeg</button>
            <button onclick="testVideoCompression()">Test Video Compression</button>
            <button onclick="testSmartCompression()">Test Smart Compression</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div>
            <input type="file" id="videoFile" accept="video/*" style="display: none;">
            <button onclick="document.getElementById('videoFile').click()">Select Video File</button>
            <span id="fileName"></span>
        </div>

        <div class="progress" id="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <div id="log" class="log">Ready to test...\n</div>
    </div>

    <script src="https://unpkg.com/@ffmpeg/ffmpeg@0.10.1/dist/ffmpeg.min.js"></script>
    <script>
        const logElement = document.getElementById('log');
        const progressElement = document.getElementById('progress');
        const progressBar = document.getElementById('progressBar');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            logElement.innerHTML = 'Log cleared...\n';
        }

        function updateProgress(percent) {
            progressElement.style.display = 'block';
            progressBar.style.width = percent + '%';
        }

        function hideProgress() {
            progressElement.style.display = 'none';
            progressBar.style.width = '0%';
        }

        // File selection handler
        document.getElementById('videoFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                document.getElementById('fileName').textContent = `Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
            }
        });

        async function testBasicFFmpeg() {
            log('Testing basic FFmpeg functionality...', 'info');
            
            try {
                // Check if FFmpeg is available
                if (typeof FFmpeg === 'undefined') {
                    throw new Error('FFmpeg library not loaded');
                }
                log('✓ FFmpeg library loaded', 'success');

                const { createFFmpeg } = FFmpeg;
                
                // Create FFmpeg instance with minimal config
                const ffmpeg = createFFmpeg({
                    log: true,
                    logger: ({ type, message }) => {
                        if (type === 'info') {
                            log(`FFmpeg: ${message}`, 'info');
                        }
                    }
                });
                log('✓ FFmpeg instance created', 'success');

                // Load FFmpeg
                log('Loading FFmpeg core (this may take 10-30 seconds)...', 'info');
                await ffmpeg.load();
                log('✓ FFmpeg loaded successfully!', 'success');

                // Test basic command
                log('Testing FFmpeg version command...', 'info');
                await ffmpeg.run('-version');
                log('✓ FFmpeg version command executed successfully!', 'success');

                log('🎉 All basic tests passed!', 'success');

            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
                console.error('Detailed error:', error);
            }
        }

        async function testVideoCompression() {
            const fileInput = document.getElementById('videoFile');
            
            if (!fileInput.files[0]) {
                log('⚠️ Please select a video file first', 'error');
                return;
            }

            const file = fileInput.files[0];
            log(`Starting compression test with: ${file.name}`, 'info');

            try {
                const { createFFmpeg, fetchFile } = FFmpeg;
                
                const ffmpeg = createFFmpeg({
                    log: true,
                    logger: ({ type, message }) => {
                        if (type === 'info' && message.includes('frame=')) {
                            // Extract progress from FFmpeg output
                            const match = message.match(/frame=\s*(\d+)/);
                            if (match) {
                                // Rough progress estimation (this is very basic)
                                const frame = parseInt(match[1]);
                                const estimatedProgress = Math.min(frame / 100, 100); // Very rough estimate
                                updateProgress(estimatedProgress);
                            }
                        }
                        log(`FFmpeg: ${message}`, 'info');
                    }
                });

                if (!ffmpeg.isLoaded()) {
                    log('Loading FFmpeg...', 'info');
                    await ffmpeg.load();
                }

                // Create file URL and load into FFmpeg
                const fileUrl = URL.createObjectURL(file);
                log('Loading video file into FFmpeg...', 'info');
                
                const fileData = await fetchFile(fileUrl);
                ffmpeg.FS('writeFile', 'input.mp4', fileData);
                log('✓ Video file loaded', 'success');

                // Get video info first
                log('Analyzing video...', 'info');
                await ffmpeg.run('-i', 'input.mp4', '-f', 'null', '-');

                // Compress with optimized settings
                log('Starting compression (this may take several minutes)...', 'info');
                updateProgress(0);

                await ffmpeg.run(
                    '-i', 'input.mp4',
                    '-c:v', 'libx264',
                    '-crf', '23',           // Better quality/size balance
                    '-preset', 'medium',
                    '-movflags', '+faststart',  // Web optimization
                    '-pix_fmt', 'yuv420p',      // Compatibility
                    '-c:a', 'aac',
                    '-b:a', '128k',         // Audio bitrate limit
                    '-avoid_negative_ts', 'make_zero',
                    'output.mp4'
                );

                log('✓ Compression completed!', 'success');
                updateProgress(100);

                // Read the compressed file
                const compressedData = ffmpeg.FS('readFile', 'output.mp4');
                const compressedBlob = new Blob([compressedData.buffer], { type: 'video/mp4' });

                // Calculate compression ratio
                const originalSize = file.size;
                const compressedSize = compressedBlob.size;
                const reduction = Math.round((1 - compressedSize / originalSize) * 100);

                log(`📊 Compression Results:`, 'success');
                log(`   Original: ${(originalSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                log(`   Compressed: ${(compressedSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                log(`   Reduction: ${reduction}%`, 'success');

                // Create download link
                const downloadUrl = URL.createObjectURL(compressedBlob);
                const downloadLink = document.createElement('a');
                downloadLink.href = downloadUrl;
                downloadLink.download = 'compressed-' + file.name;
                downloadLink.textContent = '📥 Download Compressed Video';
                downloadLink.style.cssText = 'display: block; margin: 10px 0; color: #1a73e8; text-decoration: none; font-weight: bold;';
                
                logElement.appendChild(downloadLink);

                // Cleanup
                ffmpeg.FS('unlink', 'input.mp4');
                ffmpeg.FS('unlink', 'output.mp4');
                URL.revokeObjectURL(fileUrl);

                setTimeout(hideProgress, 2000);

            } catch (error) {
                log(`❌ Compression failed: ${error.message}`, 'error');
                console.error('Detailed error:', error);
                hideProgress();
            }
        }

        async function testSmartCompression() {
            const fileInput = document.getElementById('videoFile');

            if (!fileInput.files[0]) {
                log('⚠️ Please select a video file first', 'error');
                return;
            }

            const file = fileInput.files[0];
            log(`Starting smart compression with: ${file.name}`, 'info');

            try {
                const { createFFmpeg, fetchFile } = FFmpeg;

                const ffmpeg = createFFmpeg({
                    log: true,
                    logger: ({ type, message }) => {
                        if (type === 'info' && message.includes('frame=')) {
                            const match = message.match(/frame=\s*(\d+)/);
                            if (match) {
                                const frame = parseInt(match[1]);
                                const estimatedProgress = Math.min(frame / 100, 100);
                                updateProgress(estimatedProgress);
                            }
                        }
                        log(`FFmpeg: ${message}`, 'info');
                    }
                });

                if (!ffmpeg.isLoaded()) {
                    log('Loading FFmpeg...', 'info');
                    await ffmpeg.load();
                }

                const fileUrl = URL.createObjectURL(file);
                log('Loading video file into FFmpeg...', 'info');

                const fileData = await fetchFile(fileUrl);
                ffmpeg.FS('writeFile', 'input.mp4', fileData);
                log('✓ Video file loaded', 'success');

                // Analyze video properties
                log('Analyzing video properties...', 'info');

                // Get video info
                await ffmpeg.run('-i', 'input.mp4', '-f', 'null', '-');

                // Determine optimal settings based on file size
                const fileSizeMB = file.size / (1024 * 1024);
                let crf, preset, maxBitrate;

                if (fileSizeMB > 100) {
                    // Large file - aggressive compression
                    crf = '28';
                    preset = 'medium';
                    maxBitrate = '2000k';
                    log('Large file detected - using aggressive compression', 'info');
                } else if (fileSizeMB > 50) {
                    // Medium file - balanced compression
                    crf = '25';
                    preset = 'medium';
                    maxBitrate = '3000k';
                    log('Medium file detected - using balanced compression', 'info');
                } else {
                    // Small file - gentle compression
                    crf = '23';
                    preset = 'medium';
                    maxBitrate = '4000k';
                    log('Small file detected - using gentle compression', 'info');
                }

                log(`Using settings: CRF=${crf}, preset=${preset}, max bitrate=${maxBitrate}`, 'info');
                log('Starting compression...', 'info');
                updateProgress(0);

                await ffmpeg.run(
                    '-i', 'input.mp4',
                    '-c:v', 'libx264',
                    '-crf', crf,
                    '-maxrate', maxBitrate,
                    '-bufsize', '6000k',
                    '-preset', preset,
                    '-movflags', '+faststart',
                    '-pix_fmt', 'yuv420p',
                    '-c:a', 'aac',
                    '-b:a', '128k',
                    '-avoid_negative_ts', 'make_zero',
                    'output.mp4'
                );

                log('✓ Smart compression completed!', 'success');
                updateProgress(100);

                const compressedData = ffmpeg.FS('readFile', 'output.mp4');
                const compressedBlob = new Blob([compressedData.buffer], { type: 'video/mp4' });

                const originalSize = file.size;
                const compressedSize = compressedBlob.size;
                const reduction = Math.round((1 - compressedSize / originalSize) * 100);

                log(`📊 Smart Compression Results:`, 'success');
                log(`   Original: ${(originalSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                log(`   Compressed: ${(compressedSize / 1024 / 1024).toFixed(2)} MB`, 'info');
                log(`   Reduction: ${reduction}%`, reduction > 0 ? 'success' : 'error');

                if (reduction <= 0) {
                    log('⚠️ No size reduction achieved. Original video may already be well-compressed.', 'error');
                } else {
                    log(`🎉 Successfully reduced file size by ${reduction}%!`, 'success');
                }

                const downloadUrl = URL.createObjectURL(compressedBlob);
                const downloadLink = document.createElement('a');
                downloadLink.href = downloadUrl;
                downloadLink.download = 'smart-compressed-' + file.name;
                downloadLink.textContent = '📥 Download Smart Compressed Video';
                downloadLink.style.cssText = 'display: block; margin: 10px 0; color: #1a73e8; text-decoration: none; font-weight: bold;';

                logElement.appendChild(downloadLink);

                ffmpeg.FS('unlink', 'input.mp4');
                ffmpeg.FS('unlink', 'output.mp4');
                URL.revokeObjectURL(fileUrl);

                setTimeout(hideProgress, 2000);

            } catch (error) {
                log(`❌ Smart compression failed: ${error.message}`, 'error');
                console.error('Detailed error:', error);
                hideProgress();
            }
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            log('Page loaded. Click "Test Basic FFmpeg" to start!', 'info');
        });
    </script>
</body>
</html>
