const { createFFmpeg, fetchFile } = FFmpeg;

let ffmpeg;

async function loadFFmpeg() {
  if (!ffmpeg) {
    ffmpeg = createFFmpeg({
      log: true,
      corePath: 'https://unpkg.com/@ffmpeg/core@0.11.0/dist/ffmpeg-core.js',
    });
  }
  if (!ffmpeg.isLoaded()) {
    await ffmpeg.load();
  }
  return ffmpeg;
}

async function compressVideo(videoUrl, quality, progressCallback) {
  const ffmpeg = await loadFFmpeg();

  ffmpeg.setLogger(({ type, message }) => {
    if (type === 'fferr') {
      progressCallback({ type, message });
    }
  });

  ffmpeg.setProgress(({ ratio }) => {
    progressCallback({ type: 'progress', message: `Compressing... ${Math.round(ratio * 100)}%` });
  });

  const inputFileName = 'input.mp4';
  const outputFileName = 'output.mp4';

  progressCallback({ type: 'status', message: 'Downloading video file...' });
  const fileData = await fetchFile(videoUrl);
  ffmpeg.FS('writeFile', inputFileName, fileData);

  const qualityParams = {
    high: ['-crf', '20', '-preset', 'medium'],
    medium: ['-crf', '28', '-preset', 'medium'],
    low: ['-crf', '35', '-preset', 'medium'],
  };

  const params = qualityParams[quality] || qualityParams.medium;

  progressCallback({ type: 'status', message: 'Starting compression...' });
  await ffmpeg.run('-i', inputFileName, ...params, outputFileName);

  progressCallback({ type: 'status', message: 'Reading compressed file...' });
  const data = ffmpeg.FS('readFile', outputFileName);

  ffmpeg.FS('unlink', inputFileName);
  ffmpeg.FS('unlink', outputFileName);

  return new Blob([data.buffer], { type: 'video/mp4' });
}
