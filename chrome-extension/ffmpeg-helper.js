const { createFFmpeg, fetchFile } = FFmpeg;

let ffmpeg;

async function loadFFmpeg() {
  try {
    if (!ffmpeg) {
      // Use single-threaded version to avoid SharedArrayBuffer requirement
      ffmpeg = createFFmpeg({
        log: true,
        corePath: 'https://unpkg.com/@ffmpeg/core-st@0.11.1/dist/ffmpeg-core.js',
        wasmPath: 'https://unpkg.com/@ffmpeg/core-st@0.11.1/dist/ffmpeg-core.wasm',
        workerPath: 'https://unpkg.com/@ffmpeg/core-st@0.11.1/dist/ffmpeg-core.worker.js',
      });
    }
    if (!ffmpeg.isLoaded()) {
      await ffmpeg.load();
    }
    return ffmpeg;
  } catch (error) {
    console.error('Failed to load FFmpeg:', error);
    throw new Error('Failed to load video compression engine. Please check your internet connection and try again.');
  }
}

async function compressVideo(videoUrl, quality, progressCallback) {
  try {
    const ffmpeg = await loadFFmpeg();

    ffmpeg.setLogger(({ type, message }) => {
      if (type === 'fferr') {
        progressCallback({ type, message });
      }
    });

    ffmpeg.setProgress(({ ratio }) => {
      progressCallback({ type: 'progress', ratio, message: `Compressing... ${Math.round(ratio * 100)}%` });
    });

    const inputFileName = 'input.mp4';
    const outputFileName = 'output.mp4';

    progressCallback({ type: 'status', message: 'Downloading video file...' });

    let fileData;
    try {
      fileData = await fetchFile(videoUrl);
    } catch (error) {
      throw new Error('Failed to download video file. Please check your internet connection.');
    }

    if (!fileData || fileData.length === 0) {
      throw new Error('Downloaded video file is empty or corrupted.');
    }

    ffmpeg.FS('writeFile', inputFileName, fileData);

    const qualityParams = {
      high: ['-crf', '20', '-preset', 'medium'],
      medium: ['-crf', '28', '-preset', 'medium'],
      low: ['-crf', '35', '-preset', 'medium'],
    };

    const params = qualityParams[quality] || qualityParams.medium;

    progressCallback({ type: 'status', message: 'Starting compression...' });

    try {
      await ffmpeg.run('-i', inputFileName, ...params, outputFileName);
    } catch (error) {
      throw new Error('Video compression failed. The video format may not be supported or the file may be corrupted.');
    }

    progressCallback({ type: 'status', message: 'Reading compressed file...' });

    let data;
    try {
      data = ffmpeg.FS('readFile', outputFileName);
    } catch (error) {
      throw new Error('Failed to read compressed video file.');
    }

    // Clean up files
    try {
      ffmpeg.FS('unlink', inputFileName);
      ffmpeg.FS('unlink', outputFileName);
    } catch (error) {
      console.warn('Failed to clean up temporary files:', error);
    }

    if (!data || data.length === 0) {
      throw new Error('Compressed video file is empty. Compression may have failed.');
    }

    return new Blob([data.buffer], { type: 'video/mp4' });

  } catch (error) {
    console.error('Compression error:', error);
    throw error; // Re-throw to be handled by the calling code
  }
}
