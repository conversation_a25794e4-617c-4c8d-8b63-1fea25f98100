Project Brief: "VidCompressor" Video Compression Service
(Note: "VidCompressor" is a working project name. A final brand name will be determined later.)

Product Vision
VidCompressor is a service designed for Google Photos users who need to free up storage space without deleting their memories. Integrated directly into the Google Photos web interface via a Chrome Extension, the service provides a simple and secure way to select large videos, compress them to a user-selected quality, and replace the originals.

The product will operate on a two-tier model to cater to different user needs:

Free Tier: Ad-supported, client-side compression for smaller files. This tier offers maximum privacy as media files never leave the user's computer.

Premium Tier: A subscription-based service that uses powerful cloud servers for fast, reliable compression of large files and batch processing.

Core User Flow
Installation & Authentication: The user installs the VidCompressor Chrome Extension and logs in once using their Google account ("Sign in with Google").

Enhanced UI: While browsing the Google Photos website, the extension injects a "Compress" button onto video thumbnails and pages.

Tier Selection: When a user clicks "Compress," they are presented with a clear choice:

"Compress for Free (on this computer)": Recommended for smaller files.

"Compress with Premium (on our servers)": Recommended for large files, multiple files, or for faster results.

Free Tier Flow:

The extension downloads the video file directly into the browser.

A UI overlay appears, displaying ads and a progress bar with a clear warning: "Compression is happening on your computer. Please keep this tab open. This may be slow for large files."

The in-browser WebAssembly (WASM) process compresses the video.

Once complete, the extension uploads the compressed file back to Google Photos.

Premium Tier Flow:

The extension sends the job request to our backend server.

The user is informed that the job is processing in the cloud and they can safely close the tab.

The user is prompted (e.g., via a dashboard or optional email) to approve the deletion of the original file upon completion.

System Architecture & Technology Stack
This is a hybrid architecture combining client-side processing for the free tier and a server-side microservices architecture for the premium tier.

Component

Technology & Rationale

Frontend Application

Chrome Extension built with TypeScript & React. It injects UI and manages both processing workflows. It will include FFmpeg.wasm for client-side compression in the free tier.

Backend API Server

C# with .NET 8 (ASP.NET Core). This serves the Premium Tier. It handles premium job requests, user accounts, payments, and notifications.

Job Queue (Premium Tier)

Redis. Manages the queue of server-side video compression jobs. A library like Hangfire or a custom solution with StackExchange.Redis can be used.

Video Worker (Premium Tier)

A dedicated .NET background service that pulls tasks from Redis and uses the FFmpeg command-line tool for server-side compression.

Orchestration & Hosting

Kubernetes on Google Kubernetes Engine (GKE). Hosts the backend services for the Premium Tier.

Primary Database

PostgreSQL. A reliable, open-source relational database for storing user information, subscription status, job metadata, and payment records. We will use a managed instance on GCP (Cloud SQL).

Key System Implementation Details
1. Authentication & User Management
Flow: Implement the OAuth 2.0 / OpenID Connect (OIDC) "Sign in with Google" flow, managed within the Chrome Extension.

Frontend Library: Use the @react-oauth/google library to handle the client-side authentication dance within the extension.

Backend Validation: Use Microsoft.AspNetCore.Authentication.JwtBearer to validate the JWT received from the extension. A user record is created in PostgreSQL upon first login.

2. Security & Secrets Management
All sensitive data (API secrets, DB connections) must not be in source code.

Implementation: Use Google Secret Manager as the source of truth, synced to the GKE cluster as Kubernetes Secrets via the Secrets Store CSI Driver.

3. Monetization & Payments
Free Tier: Monetized via ads displayed in the extension's UI during client-side compression. Requires integration with a suitable ad network.

Premium Tier: Subscription-based, managed by Stripe. The backend will use Stripe.net and webhooks to manage subscription status in the database.

4. Real-Time UI Notifications (Premium Tier)
Implementation: Use SignalR for .NET. The backend API will host a SignalR hub to push job status updates to the user's connected frontend client.

5. Job Resilience & Error Handling (Premium Tier)
Retry Logic: Implement automatic retries with exponential backoff for failed server-side jobs.

Dead-Letter Queue (DLQ): After a set number of retries, move failed jobs to a DLQ in Redis for manual inspection.

6. Kubernetes & Hosting Strategy (Premium Tier)
GKE Mode: Start with GKE Autopilot to optimize for cost and reduce operational overhead.

Cost Optimization: Worker node pools will use Spot VMs to leverage significant cost savings for compute-intensive compression tasks.

CI/CD: A CI/CD pipeline (e.g., using GitHub Actions) will automate the build and deployment of container images to GKE.

Pre-Development Implementation Planning
1. Infrastructure as Code (IaC) & Deployment
Infrastructure Provisioning: All cloud resources on GCP (GKE Cluster, Cloud SQL, Memorystore for Redis, etc.) will be defined and managed using Terraform. This ensures our infrastructure is version-controlled and repeatable.

Application Deployment: All applications deployed to Kubernetes (API server, workers) will be packaged and managed using Helm charts. This simplifies the deployment and configuration of our services.

2. Observability Strategy
Centralized Logging: All services must output structured, JSON-formatted logs to Google Cloud Logging.

Metrics & Monitoring: Use Prometheus and Grafana to monitor API health, queue depth, and worker throughput.

Distributed Tracing: Implement OpenTelemetry across the .NET services to trace premium job requests.

3. Local Development Experience
A Docker Compose environment will be created to run the backend services (API, PostgreSQL, Redis, Worker) locally. The Chrome Extension will be developed and loaded locally for testing.

4. FFmpeg & WebAssembly Strategy
WASM Performance Benchmarking: A critical first step is to benchmark FFmpeg.wasm. We must determine the practical file size limits for in-browser compression before it crashes the tab or creates an unacceptable user experience. This will define the upper limit for the free tier.

Server-side Hardware Acceleration: Workers running on GPU-enabled Spot VMs must utilize hardware-accelerated encoding (NVENC) via a custom-compiled FFmpeg in their Docker image.

5. API & Data Contracts
API Specification: The backend team will define the API using the OpenAPI (Swagger) standard.

Database Schema: The initial schema for PostgreSQL must be designed and agreed upon.

6. Google Photos API Integration
Quota Management: The application must gracefully handle Google's API rate limits.

Permission Handling: The application must have a flow to handle cases where a user revokes API access.